const express = require("express");
const OrderController = require("../controllers/orderController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const orderController = new OrderController();

// All order routes require authentication
router.use(verifyTokenFromCookie);

// Get user's order statistics
// GET /api/orders/stats
router.get("/stats", orderController.getUserOrderStats);

// Get user's tickets (formatted for dashboard)
// GET /api/orders/tickets
router.get("/tickets", orderController.getUserTickets);

// Get user's pending orders
// GET /api/orders/pending
router.get("/pending", orderController.getUserPendingOrders);

// Create order from cart items
// POST /api/orders/from-cart
router.post("/from-cart", orderController.createOrderFromCart);

// Create order from selected tickets (direct purchase)
// POST /api/orders/from-tickets
router.post("/from-tickets", orderController.createOrderFromTickets);

// Update order payment status
// PUT /api/orders/:orderId/payment-status
router.put(
  "/:orderId/payment-status",
  orderController.updateOrderPaymentStatus
);

// Get user's orders
// GET /api/orders
router.get("/", orderController.getUserOrders);

// Get specific order details
// GET /api/orders/:orderId
router.get("/:orderId", orderController.getOrderById);

module.exports = router;
