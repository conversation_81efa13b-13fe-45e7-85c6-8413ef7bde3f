const { PrismaClient } = require("@prisma/client");
const { v4: uuidv4 } = require("uuid");
const prisma = new PrismaClient();

class OrderService {
  static instance = null;

  static getInstance() {
    if (!OrderService.instance) {
      OrderService.instance = new OrderService();
    }
    return OrderService.instance;
  }

  // Get all orders for a user with ticket details
  async getUserOrders(userId) {
    try {
      const orders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "completed", // Only show completed orders
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true,
                    },
                  },
                },
              },
            },
          },
          tickets: true,
        },
        orderBy: {
          created_at: "desc",
        },
      });

      return orders;
    } catch (error) {
      console.error("Error fetching user orders:", error);
      throw new Error("Failed to fetch user orders");
    }
  }

  // Get user tickets with event details (formatted for dashboard)
  async getUserTickets(userId) {
    console.log("Fetching user tickets for user ID:", userId);
    try {
      const tickets = await prisma.tickets.findMany({
        where: {
          orders: {
            user_id: userId,
            payment_status: "completed",
          },
        },
        include: {
          orders: true, // Include orders to access created_at
          tickettypes: {
            include: {
              events: {
                include: {
                  locations: true,
                  genres: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      // Format tickets for frontend
      const formattedTickets = tickets.map((ticket) => {
        const event = ticket.tickettypes.events;
        const location = event.locations;

        return {
          id: ticket.ticket_id,
          eventId: event.event_id,
          eventTitle: event.title,
          eventImage: event.banner_image,
          eventDate: event.start_date, // Fixed: use start_date from schema
          eventTime: event.start_time, // Fixed: use start_time from schema
          eventLocation: {
            venue: location?.venue_name || "TBD",
            city: location?.city || "TBD",
            address: location?.address || "TBD",
          },
          ticketType: ticket.tickettypes.name,
          price: parseFloat(ticket.tickettypes.price),
          purchaseDate: ticket.orders.created_at, // Now this will work with orders included
          qrCode: ticket.qr_code,
          isValidated: ticket.is_validated,
          validationTime: ticket.validation_time,
          ticketPdf: ticket.user_ticketpdf,
        };
      });
      console.log("Formatted tickets:", formattedTickets);
      return formattedTickets;
    } catch (error) {
      console.error("Error fetching user tickets:", error);
      throw new Error("Failed to fetch user tickets");
    }
  }

  // Get order by ID (for detailed view)
  async getOrderById(orderId, userId) {
    try {
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderId,
          user_id: userId,
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true,
                    },
                  },
                },
              },
            },
          },
          tickets: true,
          users: {
            select: {
              first_name: true,
              last_name: true,
              phone_number: true,
            },
          },
        },
      });

      if (!order) {
        throw new Error("Order not found");
      }

      return order;
    } catch (error) {
      console.error("Error fetching order:", error);
      throw new Error("Failed to fetch order details");
    }
  }

  // Get order statistics for user
  async getUserOrderStats(userId) {
    try {
      const stats = await prisma.orders.aggregate({
        where: {
          user_id: userId,
          payment_status: "completed",
        },
        _count: {
          order_id: true,
        },
        _sum: {
          total_amount: true,
        },
      });

      const ticketCount = await prisma.tickets.count({
        where: {
          orders: {
            user_id: userId,
            payment_status: "completed",
          },
        },
      });

      return {
        totalOrders: stats._count.order_id || 0,
        totalSpent: parseFloat(stats._sum.total_amount || 0),
        totalTickets: ticketCount,
      };
    } catch (error) {
      console.error("Error fetching user order stats:", error);
      throw new Error("Failed to fetch order statistics");
    }
  }

  /**
   * Create order from cart items with pending payment status
   */
  async createOrderFromCart(userId, cartItems) {
    try {
      // Calculate total amount from cart items
      const totalAmount = cartItems.reduce((sum, item) => {
        return sum + parseFloat(item.tickettypes.price) * item.quantity;
      }, 0);

      // Calculate additional fees
      const organizerFees = totalAmount * 0.05; // 5% organizer fee
      const serviceFees = totalAmount * 0.1; // 10% service fee
      const additionalFees = organizerFees + serviceFees;

      // Create order with pending status
      const order = await prisma.orders.create({
        data: {
          user_id: userId,
          total_amount: totalAmount,
          additional_fees: additionalFees,
          payment_status: "pending",
          payment_method: null,
          transaction_id: null,
        },
      });

      // Create order items from cart
      const orderItems = [];
      for (const cartItem of cartItems) {
        const orderItem = await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: cartItem.ticket_type_id,
            quantity: cartItem.quantity,
            unit_price: parseFloat(cartItem.tickettypes.price),
            total_price:
              parseFloat(cartItem.tickettypes.price) * cartItem.quantity,
          },
        });
        orderItems.push(orderItem);
      }

      return {
        order,
        orderItems,
      };
    } catch (error) {
      console.error("Error creating order from cart:", error);
      throw error;
    }
  }

  /**
   * Create order from selected tickets with attendee info (for direct purchase)
   */
  async createOrderFromTickets(
    userId,
    selectedTickets,
    ticketsWithAttendeeInfo
  ) {
    try {
      // Calculate total amount
      const totalAmount = selectedTickets.reduce((sum, ticket) => {
        return sum + parseFloat(ticket.price) * ticket.quantity;
      }, 0);

      // Calculate additional fees
      const organizerFees = totalAmount * 0.05; // 5% organizer fee
      const serviceFees = totalAmount * 0.1; // 10% service fee
      const additionalFees = organizerFees + serviceFees;

      // Create order with pending status
      const order = await prisma.orders.create({
        data: {
          user_id: userId,
          total_amount: totalAmount,
          additional_fees: additionalFees,
          payment_status: "pending",
          payment_method: null,
          transaction_id: null,
        },
      });

      // Create order items
      const orderItems = [];
      for (const ticket of selectedTickets) {
        const orderItem = await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: ticket.ticketTypeId,
            quantity: ticket.quantity,
            unit_price: parseFloat(ticket.price),
            total_price: parseFloat(ticket.price) * ticket.quantity,
          },
        });
        orderItems.push(orderItem);
      }

      return {
        order,
        orderItems,
        ticketsWithAttendeeInfo,
      };
    } catch (error) {
      console.error("Error creating order from tickets:", error);
      throw error;
    }
  }

  /**
   * Update order payment status and add transaction details
   */
  async updateOrderPaymentStatus(
    orderId,
    paymentStatus,
    transactionId,
    paymentMethod = "sslcommerz"
  ) {
    try {
      const updatedOrder = await prisma.orders.update({
        where: { order_id: orderId },
        data: {
          payment_status: paymentStatus,
          transaction_id: transactionId,
          payment_method: paymentMethod,
        },
      });

      return updatedOrder;
    } catch (error) {
      console.error("Error updating order payment status:", error);
      throw error;
    }
  }

  /**
   * Get pending orders for a user
   */
  async getUserPendingOrders(userId) {
    try {
      const orders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "pending",
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: true,
                  eventcategories: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      return orders;
    } catch (error) {
      console.error("Error getting user pending orders:", error);
      throw error;
    }
  }

  /**
   * Cancel pending order
   */
  async cancelPendingOrder(orderId) {
    try {
      const updatedOrder = await prisma.orders.update({
        where: {
          order_id: orderId,
          payment_status: "pending", // Only allow canceling pending orders
        },
        data: {
          payment_status: "failed",
        },
      });

      return updatedOrder;
    } catch (error) {
      console.error("Error canceling pending order:", error);
      throw error;
    }
  }

  /**
   * Get order total including fees
   */
  getOrderTotal(order) {
    return (
      parseFloat(order.total_amount) + parseFloat(order.additional_fees || 0)
    );
  }
}

module.exports = OrderService;
